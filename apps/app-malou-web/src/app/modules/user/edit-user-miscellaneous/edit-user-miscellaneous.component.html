<div class="mat-gradient flex h-full w-full">
    <div class="malou-color-bg-white flex w-3/5 flex-col rounded-r-[50px] md:w-full md:rounded-none">
        <div class="malou-sticky-logo">
            <img alt="malou logo" [src]="'malou_logo' | imagePathResolver" />
        </div>
        <div class="hide-scrollbar grid h-full place-items-center overflow-y-auto overflow-x-hidden sm:mt-3 md:px-5">
            <div class="w-8/12 md:w-full">
                <div class="icons mb-3">
                    <img alt="Burger illustration" [src]="'Burger' | illustrationPathResolver" />
                    <img alt="Karl illustration" [src]="'Karl' | illustrationPathResolver" />
                    <img alt="Taster illustration" [src]="'Taster' | illustrationPathResolver" />
                    <img alt="Goggles illustration" [src]="'Goggles' | illustrationPathResolver" />
                </div>
                <h1 class="md:!malou-text-35--semibold malou-text-heading malou-color-text-1">{{ 'misc.welcome' | translate }}</h1>
                <h2 class="md:!malou-text-13--regular malou-text-15--regular mb-11">{{ 'misc.connect_yourself' | translate }}</h2>
                <form class="malou-form-flex" [formGroup]="miscForm" (ngSubmit)="updateMisc()">
                    <app-input-text
                        class="mb-5"
                        formControlName="name"
                        [title]="'misc.name' | translate"
                        [placeholder]="'misc.name' | translate"
                        [autocapitalize]="'none'">
                    </app-input-text>
                    <app-input-text
                        class="mb-5"
                        formControlName="lastname"
                        [title]="'misc.lastname' | translate"
                        [placeholder]="'misc.lastname' | translate">
                    </app-input-text>

                    <div class="mb-10" formGroupName="phone">
                        <div class="flex gap-x-2">
                            <div class="w-1/3">
                                <app-select
                                    formControlName="prefix"
                                    [title]="'admin.users.phone_prefix' | translate"
                                    [placeholder]="'admin.users.phone_prefix' | translate"
                                    [values]="PHONE_CODES"
                                    [displayWith]="phoneCodesDisplayWith">
                                </app-select>
                            </div>
                            <div class="grow">
                                <app-input-text
                                    formControlName="digits"
                                    [inputType]="'number'"
                                    [errorMessage]="digitsErrorMessage()"
                                    [title]="'admin.users.phone' | translate"
                                    [prefix]="formattedPrefix()"
                                    [placeholder]="'admin.users.phone' | translate">
                                </app-input-text>
                            </div>
                        </div>
                    </div>

                    <button class="malou-btn-raised--primary" type="submit" mat-raised-button [disabled]="!miscForm.valid">
                        {{ 'common.validate' | translate }}
                    </button>
                </form>
            </div>
        </div>
    </div>
    <div class="w-2/5 items-center justify-center md:hidden md:w-0">
        <div class="flex h-full flex-col items-center justify-around">
            <p class="malou-text-title md:malou-text-section-title malou-color-white mt-4 px-14 text-center">
                {{ 'login.digital_for_growth' | translate }}
            </p>
            <img class="px-2" alt="Screen illustration" [src]="'Screen' | illustrationPathResolver" />
            <div class="malou-color-white malou-text-20--regular mb-4">
                <div>
                    {{ 'login.more' | translate }} <strong>{{ 'login.time' | translate }}</strong
                    >,
                </div>
                <div>
                    {{ 'login.more' | translate }} <strong>{{ 'login.visibility' | translate }}</strong
                    >,
                </div>
                <div>
                    {{ 'login.more' | translate }} <strong>{{ 'login.clients' | translate }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>
